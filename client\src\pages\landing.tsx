import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { AppHeader } from "@/components/layout/app-header";

export default function LandingPage() {
  const { user, isLoading } = useAuth();
  const [_, navigate] = useLocation();

  // Redirect to dashboard if user is logged in
  useEffect(() => {
    if (user && !isLoading) {
      navigate("/");
    }
  }, [user, isLoading, navigate]);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <AppHeader showLoginButtons={true} showUserMenu={false} />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-20">
        <div className="container mx-auto flex flex-col lg:flex-row items-center gap-12">
          <div className="lg:w-1/2 space-y-6">
            <h1 className="text-4xl lg:text-5xl font-bold tracking-tight text-primary-900">
              Craft Academic Excellence with Inspire
            </h1>
            <p className="text-xl text-primary-800">
              The intelligent writing assistant for students, researchers, and academics that simplifies the paper writing process from outline to final draft.
            </p>
            <div className="pt-4">
              <Button 
                size="lg" 
                onClick={() => navigate("/auth")}
                className="px-8 py-6 text-lg rounded-lg"
              >
                Get Started for Free
              </Button>
            </div>
          </div>
          <div className="lg:w-1/2">
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="p-4 bg-primary-800 text-white">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
              </div>
              <div className="p-6 flex flex-col">
                <div className="flex border-b border-gray-200 pb-4 mb-4 gap-4">
                  <div className="w-1/3 border-r pr-4">
                    <div className="font-medium mb-2 text-primary-700">Outline</div>
                    <div className="space-y-2">
                      <div className="bg-gray-100 h-6 rounded"></div>
                      <div className="bg-gray-100 h-6 rounded"></div>
                      <div className="bg-gray-100 h-6 rounded ml-4"></div>
                      <div className="bg-gray-100 h-6 rounded"></div>
                      <div className="bg-gray-100 h-6 rounded ml-4"></div>
                    </div>
                  </div>
                  <div className="w-1/3 border-r pr-4">
                    <div className="font-medium mb-2 text-primary-700">Notes</div>
                    <div className="space-y-2">
                      <div className="bg-primary-50 p-2 rounded border border-primary-100">
                        <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded mb-1"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="bg-primary-50 p-2 rounded border border-primary-100">
                        <div className="h-4 bg-gray-200 rounded mb-2 w-2/3"></div>
                        <div className="h-3 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  </div>
                  <div className="w-1/3">
                    <div className="font-medium mb-2 text-primary-700">Writing</div>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-center items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-primary-100"></div>
                    <div className="text-sm text-gray-500">Outline</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-primary-200"></div>
                    <div className="text-sm text-gray-500">Take Notes</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-primary-300"></div>
                    <div className="text-sm text-gray-500">Write & Format</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Streamlined Academic Writing Process</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
                <i className="ri-file-list-line text-2xl text-primary"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">Structured Outlines</h3>
              <p className="text-gray-600">
                Create hierarchical outlines with intuitive drag-and-drop functionality. Organize your thoughts visually for better paper structure.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
                <i className="ri-sticky-note-line text-2xl text-primary"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">Linked Research Notes</h3>
              <p className="text-gray-600">
                Connect your research notes directly to outline sections. Never lose track of important information or sources.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
                <i className="ri-quill-pen-line text-2xl text-primary"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">Smart Writing Tools</h3>
              <p className="text-gray-600">
                Spell check, grammar assistance, and citation management built in. Create polished, professional papers with ease.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-primary-900 text-white py-16">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Elevate Your Academic Writing?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join thousands of students and researchers who use Inspire to create better papers in less time.
          </p>
          <Button 
            size="lg" 
            variant="outline"
            onClick={() => navigate("/auth")}
            className="px-8 py-6 text-lg rounded-lg bg-transparent border-white text-white hover:bg-white hover:text-primary-900"
          >
            Get Started Now
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-50 py-8 border-t border-gray-200">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <p className="text-sm text-gray-500">
                © 2025 Inspire. All rights reserved.
              </p>
            </div>
            <div className="flex gap-6">
              <a href="#" className="text-gray-500 hover:text-primary">Terms</a>
              <a href="#" className="text-gray-500 hover:text-primary">Privacy</a>
              <a href="#" className="text-gray-500 hover:text-primary">Contact</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}