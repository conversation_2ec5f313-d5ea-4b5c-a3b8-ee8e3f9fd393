import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import NewMinimap from '../NewMinimap'; // Updated import for NewMinimap
import { OutlineItem } from './item';
import { OutlineItem as OutlineItemType, Note } from '@/lib/types';
import { calculateOutlineNumbers } from '@/lib/utils/outline';
// import NoteEditorModal from '@/components/notes/NoteEditorModal'; // Removed
import { ConfirmModal } from '@/components/ui/confirm-modal';
import { nanoid } from 'nanoid';

import { Input } from "@/components/ui/input";
import { useToast } from '@/hooks/use-toast';

interface OutlinePanelProps {
  outline: OutlineItemType[];
  onOutlineChange: (outline: OutlineItemType[]) => void;
  onAddItem: (parentId?: string) => void;
  onImportOutline: () => void;
  // Note-related props
  notes: Note[];
  onNoteCreate: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => Note; // Updated prop signature
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onNoteDelete: (noteId: string) => void; // Already present
  onNoteDuplicate: (noteId: string) => void; // Added
  onFileUpload: (file: File) => Promise<string>;
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void;
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void;
  popOutPanel: () => void;
  isPoppedOut?: boolean;
}

export function OutlinePanel({
  outline,
  onOutlineChange,
  onAddItem,
  onImportOutline,
  // Note-related props
  notes,
  onNoteCreate,
  onNoteUpdate,
  onNoteDelete, // Existing
  onNoteDuplicate, // Added
  onFileUpload,
  onLinkNoteToOutlineItem,
  onDeleteOutlineItemWithNotes,
  popOutPanel,
  isPoppedOut,
}: OutlinePanelProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [outlineSearchTerm, setOutlineSearchTerm] = useState("");

  // Updated SearchResult structure
  interface SearchResult {
    item: OutlineItemType;
    matchDetails: {
      type: 'outlineTitle' | 'noteTitle' | 'noteContent';
      noteId?: string;
    };
  }
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [currentSearchResultIndex, setCurrentSearchResultIndex] = useState(-1);

  // Simple drag and drop state
  const [draggedItem, setDraggedItem] = useState<OutlineItemType | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<{id: string, position: 'before' | 'after' | 'child'} | null>(null);
  const [hoveredItemPath, setHoveredItemPath] = useState<string[]>([]);
  // const [editingNote, setEditingNote] = useState<Note | null>(null); // Removed: State for NoteEditorModal
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const [outlineItemToDeleteId, setOutlineItemToDeleteId] = useState<string | null>(null);
  const { toast } = useToast();

  // Function to get flat list of search results
  const getFlatSearchResults = (items: OutlineItemType[], searchTerm: string, allNotes: Note[]): SearchResult[] => {
    let results: SearchResult[] = [];
    if (!searchTerm) return results;
    const lowerSearchTerm = searchTerm.toLowerCase();

    const recurse = (currentItems: OutlineItemType[]) => {
      for (const item of currentItems) {
        // Check outline item title
        if (item.title.toLowerCase().includes(lowerSearchTerm)) {
          results.push({
            item,
            matchDetails: { type: 'outlineTitle' }
          });
          // Even if outline title matches, continue to check its notes and children for more specific highlights
        }

        // Check linked notes
        const linkedNotes = allNotes.filter(note => note.linkedOutlineId === item.id);
        for (const note of linkedNotes) {
          let noteAdded = false;
          if (note.title.toLowerCase().includes(lowerSearchTerm)) {
            // Check if this item+noteTitle combination is already added for a different reason (e.g. outlineTitle match)
            // We only want to add this outline item once per unique match reason if possible,
            // or decide if an outline item can appear multiple times in search if it matches in multiple places.
            // For now, let's assume one result per outline item, but prioritize note match details if a note matches.
            // A more sophisticated approach might allow multiple entries or aggregate matchDetails.
            // Current simplification: if outline title matched, we already have 'item'.
            // If we find a note match, we might want to *update* the existing result for this 'item' or add a new one.
            // For simplicity: add a new result for each distinct match location.
            // This means an OutlineItem can appear multiple times if its title AND a note within it match.
            results.push({
              item,
              matchDetails: { type: 'noteTitle', noteId: note.id }
            });
            noteAdded = true;
          }
          // Check note content only if title didn't match, to avoid duplicate entries for the same note.
          // Or, allow multiple if we want to highlight title and content separately.
          // For now, let's say a note title match is sufficient for that note.
          if (!noteAdded && note.content && note.content.toLowerCase().includes(lowerSearchTerm)) {
            results.push({
              item,
              matchDetails: { type: 'noteContent', noteId: note.id }
            });
          }
        }

        if (item.children && item.children.length > 0) {
          recurse(item.children);
        }
      }
    };

    recurse(items);
    return results;
  };

  useEffect(() => {
    if (outlineSearchTerm) {
      const results = getFlatSearchResults(outline, outlineSearchTerm, notes);
      setSearchResults(results);
      setCurrentSearchResultIndex(results.length > 0 ? 0 : -1);
    } else {
      setSearchResults([]);
      setCurrentSearchResultIndex(-1);
    }
  }, [outlineSearchTerm, outline, notes]);

  // Function to filter outline items based on search term (for hierarchical display)
  const filterOutline = (items: OutlineItemType[], searchTerm: string, allNotes: Note[]): OutlineItemType[] => {
    if (!searchTerm) {
      return items; // Return all items if search term is empty
    }

    return items.reduce((acc, item) => {
      // Check if item title matches
      const titleMatches = item.title.toLowerCase().includes(searchTerm.toLowerCase());

      // Check if any linked notes match
      const linkedNotes = allNotes.filter(note => note.linkedOutlineId === item.id);
      const noteMatches = linkedNotes.some(note =>
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.content.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // Recursively filter children
      let filteredChildren: OutlineItemType[] = [];
      if (item.children && item.children.length > 0) {
        filteredChildren = filterOutline(item.children, searchTerm, allNotes);
      }

      // Include item if it matches directly or has matching children
      if (titleMatches || noteMatches || filteredChildren.length > 0) {
        acc.push({
          ...item,
          children: filteredChildren // Use the filtered children
        });
      }
      return acc;
    }, [] as OutlineItemType[]);
  };

  const filteredOutline = useMemo(() => {
    return filterOutline(outline, outlineSearchTerm, notes);
  }, [outline, outlineSearchTerm, notes]);

  const [draggedItemGhostInfo, setDraggedItemGhostInfo] = useState<{
    parentId: string | null; // ID of the parent under which ghost appears
    index: number;           // Index in that parent's children array
    depth: number;           // Calculated depth for the ghost
  } | null>(null);

  // Helper function to get depth (can be memoized or moved if complex)
  const getItemDepth = (items: OutlineItemType[], itemId: string, currentDepth = 0): number => {
    for (const item of items) {
      if (item.id === itemId) return currentDepth;
      if (item.children) {
        const foundDepth = getItemDepth(item.children, itemId, currentDepth + 1);
        if (foundDepth !== -1) return foundDepth;
      }
    }
    return -1;
  };

  const outlineToUseForGhost = outlineSearchTerm ? filteredOutline : outline;

  const outlineWithGhost = useMemo(() => {
    if (!draggedItemGhostInfo || !activeItem) {
      // If there's a search term, use filteredOutline, otherwise use the original outline.
      // And calculate numbers on the result.
      return calculateOutlineNumbers(outlineToUseForGhost);
    }

    const { parentId: targetParentId, index: targetIndex } = draggedItemGhostInfo;
    const ghostItemData = { ...activeItem, id: 'ghost-item-id', number: "👻" }; // Customize ghost appearance

    // Base the tempOutline on filteredOutline if a search term exists, otherwise use the original outline.
    let tempOutline = JSON.parse(JSON.stringify(outlineToUseForGhost));

    const injectGhost = (items: OutlineItemType[], currentParentId: string | null): boolean => {
      if (currentParentId === targetParentId) {
        items.splice(targetIndex, 0, ghostItemData);
        return true;
      }
      for (let item of items) {
        if (item.children && injectGhost(item.children, item.id)) {
          return true;
        }
      }
      return false;
    };

    injectGhost(tempOutline, null); // Start search from root (null parentId)
    // It's important that calculateOutlineNumbers can handle the ghost item or is called carefully
    return calculateOutlineNumbers(tempOutline);
  }, [outlineToUseForGhost, draggedItemGhostInfo, activeItem]);

  // useEffect for editingNote removed as editingNote state is removed.

  const triggerInitialMediaUpload = async (note: Note) => {
    if (!note || !['image', 'video', 'file'].includes(note.type || '')) {
      return;
    }

    let acceptTypes = '';
    switch (note.type) {
      case 'image': acceptTypes = 'image/*'; break;
      case 'video': acceptTypes = 'video/*'; break;
      case 'file': acceptTypes = '*'; break; // Or more specific: '.pdf,.doc,.docx,.txt,.md'
      default: return; // Should not happen if check above is correct
    }

    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = acceptTypes;
    fileInput.style.display = 'none';

    fileInput.onchange = async (e) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const file = target.files[0];
        const noteTypeName = note.type ? note.type.charAt(0).toUpperCase() + note.type.slice(1) : "File";
        toast({ title: `Uploading ${noteTypeName}...`, description: file.name });

        try {
          const uploadedUrl = await onFileUpload(file); // Use onFileUpload prop
          if (uploadedUrl) {
            let updatedNoteData: Partial<Note> = {
              primaryAssetUrl: uploadedUrl,
              updatedAt: new Date().toISOString(),
            };

            if (note.type === 'image') updatedNoteData.imageUrls = [uploadedUrl];
            else if (note.type === 'video') updatedNoteData.videoUrls = [uploadedUrl];
            else if (note.type === 'file') updatedNoteData.fileUrls = [uploadedUrl];

            // Default titles are set by useDocument's createNote.
            // Typically "New Note" or type-specific like "Image Note".
            const defaultTitles = ["New Note", "Image Note", "Video Note", "File Note", note.type];
            if (note.title && defaultTitles.includes(note.title)) {
              updatedNoteData.title = file.name;
            }

            const finalUpdatedNote = { ...note, ...updatedNoteData };
            onNoteUpdate(finalUpdatedNote, true); // Use onNoteUpdate prop
            toast({ title: `${noteTypeName} Uploaded`, description: `${file.name} has been successfully attached.` });
          }
        } catch (uploadError) {
          console.error(`OutlinePanel: Failed to upload ${note.type} file:`, uploadError);
          toast({ title: `Error Uploading ${noteTypeName}`, description: String(uploadError), variant: "destructive" });
        }
      }
      if (fileInput.parentElement) {
        document.body.removeChild(fileInput);
      }
    };

    fileInput.oncancel = () => {
      if (fileInput.parentElement) {
        document.body.removeChild(fileInput);
      }
      // Optionally, if the note was created purely for this upload and is now empty,
      // you could consider deleting it, but that adds complexity.
      // For now, an empty media note will remain.
    };

    document.body.appendChild(fileInput);
    fileInput.click();
  };

  const handleAddNoteToOutlineItem = async (outlineItemId: string, noteType: 'text' | 'image' | 'video' | 'file') => {
    const newNote = onNoteCreate(outlineItemId, { type: noteType });
    if (newNote) {
      if (['image', 'video', 'file'].includes(newNote.type || '')) {
        // Add a small delay to ensure the state updates from onNoteCreate propagate if needed,
        // though direct object passing should be fine.
        setTimeout(() => triggerInitialMediaUpload(newNote), 100);
      }
    }
  };

  // const handleOpenNoteEditor = (note: Note) => { // Removed
  //   setEditingNote(note); // Removed
  // }; // Removed

  // IMPROVEMENT 1: Use only PointerSensor with very permissive settings
  // COMPLETELY REVAMPED SENSOR CONFIGURATION
  // Use a simpler approach with only PointerSensor to fix the drag reliability issues
  // The key issue appears to be that the PointerSensor settings were too sensitive
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Use delay instead of distance for activation - this ensures the user
      // deliberately wants to drag by requiring a short press before drag starts
      activationConstraint: {
        delay: 50,      // Very short delay - just enough to prevent accidental drags
        tolerance: 500, // Extremely permissive tolerance during press - accepts virtually any movement
      }
    }),
    // Keep keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // We'll use this ref to track if a drag operation is in progress
  const isDraggingRef = useRef(false);
  
  // Add global mouse up handler to ensure drag operation is always properly cancelled
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      // If we were dragging but the mouse was released outside our component
      if (isDraggingRef.current) {
        console.log("Global mouse up detected while dragging - cleaning up");
        
        // Reset dragging state
        isDraggingRef.current = false;
        
        // Remove visual indicators
        document.body.classList.remove('dragging-mode');
        document.body.classList.remove('disable-websocket-updates');
        
        // document.querySelectorAll('.drop-target-before, .drop-target-after, .drop-target-parent').forEach(el => {
        //   el.classList.remove('drop-target-before', 'drop-target-after', 'drop-target-parent');
        // });
        
        document.querySelectorAll('.dragging').forEach(el => {
          el.classList.remove('dragging');
        });
        
        // Reset cursor
        document.body.style.cursor = '';
        document.documentElement.style.cursor = '';
        
        // Re-enable WebSocket updates and trigger reconnect
        document.body.classList.remove('disable-websocket-updates');
        window.dispatchEvent(new CustomEvent('reconnect-websocket'));
        
        // Force reset active state
        setActiveId(null);
        setActiveItem(null);
        setDropTarget(null);
      }
    };
    
    // Add mouse up listener to entire document
    document.addEventListener('mouseup', handleGlobalMouseUp);
    
    // Clean up on unmount
    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, []);

  // Find an item by ID in the nested structure
  const findItemById = (items: OutlineItemType[], id: string): OutlineItemType | null => {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children && item.children.length > 0) {
        const found = findItemById(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };
  
  // Find the complete path (all ancestor IDs) to an item
  const findPathToItem = (
    items: OutlineItemType[], 
    targetId: string, 
    currentPath: string[] = []
  ): string[] => {
    for (const item of items) {
      // Create a new path including the current item
      const newPath = [...currentPath, item.id];
      
      // If this is the target, return the path
      if (item.id === targetId) {
        return newPath;
      }
      
      // If this item has children, search them
      if (item.children && item.children.length > 0) {
        const pathInChildren = findPathToItem(item.children, targetId, newPath);
        if (pathInChildren.length > 0) {
          return pathInChildren;
        }
      }
    }
    
    // Target not found in this branch
    return [];
  };

  // Function to find the parent of an item
  const findParentOfItem = (items: OutlineItemType[], id: string): { parent: OutlineItemType | null, index: number } => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.children) {
        for (let j = 0; j < item.children.length; j++) {
          if (item.children[j].id === id) {
            return { parent: item, index: j };
          }
        }
        const result = findParentOfItem(item.children, id);
        if (result.parent) {
          return result;
        }
      }
    }
    return { parent: null, index: -1 };
  };

  // Function to remove an item from the tree
  const removeItem = (items: OutlineItemType[], id: string): { items: OutlineItemType[], removed: OutlineItemType | null } => {
    console.log(`---REMOVE ITEM OPERATION START--- (ID: ${id})`);
    
    // First, find the item we want to remove so we can return it
    const itemToRemove = findItemById(items, id);
    if (!itemToRemove) {
      console.error(`Item with ID ${id} not found for removal`);
      return { items, removed: null };
    }
    
    console.log(`Found item to remove: "${itemToRemove.title}"`);
    
    // Deep clone the items array to avoid mutations
    let result = JSON.parse(JSON.stringify(items));
    
    // Check if item is at the top level - simple case
    const topLevelIndex = result.findIndex((item: OutlineItemType) => item.id === id);
    if (topLevelIndex !== -1) {
      console.log(`Item is at top level, index: ${topLevelIndex}`);
      // Create a new array without the item
      result.splice(topLevelIndex, 1);
      return {
        items: result,
        removed: itemToRemove
      };
    }
    
    // Helper function to recursively remove an item from any level
    const removeFromChildren = (items: OutlineItemType[], targetId: string): boolean => {
      // Check each item at this level
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        
        // Skip items with no children
        if (!item.children || item.children.length === 0) {
          continue;
        }
        
        // Get a reference to the children array with proper type assertion
        const childrenArray: OutlineItemType[] = item.children;
        
        // Check direct children first
        const childIndex = childrenArray.findIndex(child => child.id === targetId);
        
        if (childIndex !== -1) {
          // Found the target as a direct child - remove it
          console.log(`Found item to remove as child of "${item.title}" at index ${childIndex}`);
          childrenArray.splice(childIndex, 1);
          return true;
        }
        
        // If not a direct child, search deeper
        const removed = removeFromChildren(childrenArray, targetId);
        if (removed) {
          return true;
        }
      }
      
      return false; // Not found in this branch
    };
    
    // Attempt to remove the item from the tree
    const removed = removeFromChildren(result, id);
    
    if (!removed) {
      console.error(`Failed to remove item "${itemToRemove.title}" (ID: ${id}) from the tree`);
      return { items, removed: null };
    }
    
    console.log(`Successfully removed item "${itemToRemove.title}" from the tree`);
    console.log(`---REMOVE ITEM OPERATION END---`);
    
    return {
      items: result,
      removed: itemToRemove
    };
  };

  // Replace an item in the tree with a new version
  const replaceItem = (items: OutlineItemType[], id: string, newItem: OutlineItemType): OutlineItemType[] => {
    return items.map(item => {
      if (item.id === id) {
        return newItem;
      }
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: replaceItem(item.children, id, newItem)
        };
      }
      // If no children or no match, return the item as is
      return item;
    });
  };

  // Add an item as a child of another item
  const addItemAsChild = (items: OutlineItemType[], parentId: string, item: OutlineItemType): OutlineItemType[] => {
    // Clone the entire items array first for safety
    let result = JSON.parse(JSON.stringify(items));
    
    console.log(`---PARENT ADD OPERATION START---`);
    console.log(`Adding item '${item.title}' as child of parent with ID: ${parentId}`);
    
    // Helper function to add a child to a specific parent in the tree
    const addChildToParent = (items: OutlineItemType[], parentId: string, childItem: OutlineItemType): boolean => {
      // Find the parent directly in this array
      const parentIndex = items.findIndex(item => item.id === parentId);
      
      if (parentIndex !== -1) {
        // Found the parent at this level
        console.log(`Parent found directly at index ${parentIndex} in current level`);
        
        // Initialize children array if it doesn't exist
        if (!items[parentIndex].children) {
          items[parentIndex].children = [];
        }
        
        // Add the child
        items[parentIndex].children.push(childItem);
        console.log(`Child added to parent "${items[parentIndex].title}"`);
        return true;
      }
      
      // If not found at this level, look in child arrays
      for (let i = 0; i < items.length; i++) {
        const currentItem = items[i];
        
        // Handle potential children with extra safety
        try {
          // Skip if no children array
          if (!currentItem.children) continue; 
          
          // Skip empty children arrays
          if (currentItem.children.length === 0) continue;
          
          // We've verified children exists and is not empty - we can clone it
          const childrenCopy = [...currentItem.children];
          
          // Try to add the child in this item's children
          const added = addChildToParent(childrenCopy, parentId, childItem);
          if (added) {
            // Successfully added to this item's children - update the original item
            currentItem.children = childrenCopy;
            return true; // Child was added in a deeper level
          }
        } catch (err) {
          console.error("Error processing children in addChildToParent:", err, currentItem);
          continue; // Skip this item on error
        }
      }
      
      // If we reach here, the parent wasn't found
      return false;
    };
    
    // Attempt to add the child
    const success = addChildToParent(result, parentId, item);
    
    if (!success) {
      console.error(`Failed to find parent with ID ${parentId} to add child "${item.title}"`);
      // If parent not found, append to the end as fallback
      result.push(item);
    }
    
    console.log(`Final outline structure:`, result);
    console.log(`---PARENT ADD OPERATION END---`);
    
    return result;
  };
  
  // Add an item after another item at the same level
  const addItemAfter = (items: OutlineItemType[], targetId: string, item: OutlineItemType): OutlineItemType[] => {
    // Handle top-level items
    const targetIndex = items.findIndex(currItem => currItem.id === targetId);
    if (targetIndex !== -1) {
      // Create a new array with the item inserted right after the target
      const result = [...items];
      result.splice(targetIndex + 1, 0, item);
      return result;
    }
    
    // Handle nested items
    return items.map(currentItem => {
      // Skip if no children array
      if (!currentItem.children) return currentItem;
        
      // Continue only if children exist and not empty
      if (currentItem.children.length > 0) {
        const childIndex = currentItem.children.findIndex(child => child.id === targetId);
        if (childIndex !== -1) {
          // Clone the children array and insert item after the target child
          const newChildren = [...currentItem.children];
          newChildren.splice(childIndex + 1, 0, item);
          
          return {
            ...currentItem,
            children: newChildren
          };
        }
        
        // Use the non-null assertion operator to tell TypeScript that children is not undefined
        const children = currentItem.children!;
        return {
          ...currentItem,
          children: addItemAfter(children, targetId, item)
        };
      }
      return currentItem;
    });
  };

  // Add an item before another item at the same level
  const addItemBefore = (items: OutlineItemType[], targetId: string, item: OutlineItemType): OutlineItemType[] => {
    // Handle top-level items
    const targetIndex = items.findIndex(currItem => currItem.id === targetId);
    if (targetIndex !== -1) {
      // Create a new array with the item inserted before the target
      const result = [...items];
      result.splice(targetIndex, 0, item);
      return result;
    }
    
    // Handle nested items
    return items.map(currentItem => {
      // Skip if no children array
      if (!currentItem.children) return currentItem;
      
      // Continue only if children exist and not empty
      if (currentItem.children.length > 0) {
        const childIndex = currentItem.children.findIndex(child => child.id === targetId);
        if (childIndex !== -1) {
          // Clone the children array and insert item before the target child
          const newChildren = [...currentItem.children];
          newChildren.splice(childIndex, 0, item);
          
          return {
            ...currentItem,
            children: newChildren
          };
        }
        
        // Use the non-null assertion operator to tell TypeScript that children is not undefined
        const children = currentItem.children!; 
        return {
          ...currentItem,
          children: addItemBefore(children, targetId, item)
        };
      }
      return currentItem;
    });
  };

  // IMPROVEMENT 2: Enhanced handleDragStart with better visual feedback and stability
  const handleDragStart = (event: DragStartEvent) => {
    console.log("------ NEW DRAG OPERATION ------");
    console.log("Drag start detected for item:", event.active.id);
    const { active } = event;
    
    try {
      // First, validate that we have a valid active ID
      if (!active || !active.id) {
        console.error("Invalid drag start - missing active ID");
        return;
      }
      
      const activeId = active.id as string;
      
      // CRITICAL: Cancel any ongoing drag first to ensure clean start
      if (isDraggingRef.current) {
        console.log("Canceling previous incomplete drag operation...");
        isDraggingRef.current = false;
        document.body.style.cursor = '';
        document.body.classList.remove('dragging-mode');
        document.body.classList.remove('disable-websocket-updates');
        
        // Remove all visual indicators
        // document.querySelectorAll('.drop-target-before, .drop-target-after, .drop-target-parent').forEach(el => {
        //   el.classList.remove('drop-target-before', 'drop-target-after', 'drop-target-parent');
        // });
        document.querySelectorAll('.dragging').forEach(el => {
          el.classList.remove('dragging');
        });
      }
      
      // Now set the new active ID
      setActiveId(activeId);
      
      // Set the dragging flag to true - MUST happen before any other operations
      isDraggingRef.current = true;
      
      // Set cursor to grabbing - essential for drag visual feedback
      document.body.style.cursor = 'grabbing';
      
      // Add dragging-mode class to body for visual feedback
      document.body.classList.add('dragging-mode');
      
      // Add global drag state class for CSS styling
      document.documentElement.classList.add('outline-dragging-active');
      
      // Disable WebSocket updates during drag operations to prevent conflicts
      // This class is checked in shouldBlockUpdates() function in use-collaboration hook
      document.body.classList.add('disable-websocket-updates');
      console.log("WebSocket updates DISABLED for drag operation - safe to drag");
      
      // Clear any existing safety timer first to prevent multiple timers
      if ((window as any).dragResetTimer) {
        clearTimeout((window as any).dragResetTimer);
        (window as any).dragResetTimer = null;
      }
      
      // Create a new safety timer with MUCH longer timeout (20s - giving plenty of time for drag operations)
      (window as any).dragResetTimer = setTimeout(() => {
        console.log("Safety cleanup timer firing");
        if (isDraggingRef.current) {
          // Reset all drag-related state
          isDraggingRef.current = false;
          document.body.style.cursor = '';
          document.body.classList.remove('dragging-mode');
          document.body.classList.remove('disable-websocket-updates');
          document.documentElement.classList.remove('outline-dragging-active');
          
          // Clear visual indicators
          // document.querySelectorAll('.drop-target-before, .drop-target-after, .drop-target-parent').forEach(el => {
          //   el.classList.remove('drop-target-before', 'drop-target-after', 'drop-target-parent');
          // });
          
          document.querySelectorAll('.dragging').forEach(el => {
            el.classList.remove('dragging');
          });
          
          // Reset state
          setActiveId(null);
          setActiveItem(null);
          setDropTarget(null);
          
          console.log("SAFETY TIMEOUT: Drag operation force-completed");
        }
      }, 20000); // 20-second safety timeout - much more lenient
      
      // Find the item being dragged in our outline data
      const draggedItem = findItemById(outline, activeId);
      if (draggedItem) {
        console.log("Dragging item:", draggedItem.title);
        setActiveItem(draggedItem);
        
        // Add a visual class to the dragged element
        const draggedElement = document.querySelector(`[data-id="${activeId}"]`);
        if (draggedElement) {
          draggedElement.classList.add('dragging');
        }
      } else {
        console.warn(`Could not find item with ID ${activeId} in outline data`);
      }
    } catch (error) {
      // If anything goes wrong, make sure we don't leave the app in a bad state
      console.error("Error in drag start handler:", error);
      isDraggingRef.current = false;
      document.body.style.cursor = '';
      document.body.classList.remove('dragging-mode');
      document.body.classList.remove('disable-websocket-updates');
      
      // Reset state
      setActiveId(null);
      setActiveItem(null);
      setDropTarget(null);
    }
  };

  // Handle drag over
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    console.log("DRAG OVER EVENT:", {
      activeId: active.id,
      overId: over?.id,
      overData: over?.data.current,
      clientOffset: (event as any).clientOffset,
    });
    
    // First, remove any existing target classes from all elements - REMOVED
    // document.querySelectorAll('.drop-target-before, .drop-target-after, .drop-target-parent').forEach(el => {
    //   el.classList.remove('drop-target-before', 'drop-target-after', 'drop-target-parent');
    // });
    
    if (!over || active.id === over.id) {
      setDropTarget(null);
      // It's important to also clear the ghost info if we are hovering over self or not over anything.
      setDraggedItemGhostInfo(null);
      return;
    }

    // Check if we're over a drop zone
    const overData = over.data.current;
    const overId = over.id as string;
    
    // Handle explicit drop zones
    if (overData?.type === 'dropzone') {
      const targetId = overData.target as string;
      const action = overData.action as 'before' | 'after' | 'parent';
      
      console.log("Over explicit drop zone:", { targetId, action });
      
      setDropTarget({ 
        id: targetId, 
        type: action 
      });
      
      // Add visual indicator class to parent element - REMOVED
      // const targetElement = document.querySelector(`[data-id="${targetId}"]`);
      // if (targetElement) {
      //   if (action === 'before') {
      //     targetElement.classList.add('drop-target-before');
      //   } else if (action === 'after') {
      //     targetElement.classList.add('drop-target-after');
      //   } else if (action === 'parent') {
      //     targetElement.classList.add('drop-target-parent');
      //   }
      // }

      // Logic for setting ghost placeholder (this part should remain and be enhanced)
      if (activeItem && targetId !== activeItem.id) {
        const targetItemDepth = getItemDepth(outline, targetId);
        let finalParentId: string | null;
        let finalIndex: number;
        let finalDepth: number;

        if (action === 'parent') {
            finalParentId = targetId;
            const parentNode = findItemById(outline, targetId);
            finalIndex = parentNode?.children?.length || 0;
            finalDepth = targetItemDepth + 1;
        } else { // 'before' or 'after'
            const { parent: actualParent, index: siblingIndex } = findParentOfItem(outline, targetId);
            finalParentId = actualParent ? actualParent.id : null;
            finalDepth = targetItemDepth;
            finalIndex = action === 'before' ? siblingIndex : siblingIndex + 1;
        }

        setDraggedItemGhostInfo({ parentId: finalParentId, index: finalIndex, depth: finalDepth });
      } else {
        setDraggedItemGhostInfo(null);
      }
      return;
    }
    
    // SIMPLIFIED HANDLER: Always make it a parent relationship when over an item
    // This section needs to correctly set draggedItemGhostInfo as well.
    if (overData?.type === 'item') {
      // const targetElement = document.querySelector(`[data-id="${overId}"]`); // Not needed for class
      setDropTarget({ id: overId, type: 'parent' }); // Assuming 'parent' drop for over item
      // if (targetElement) targetElement.classList.add('drop-target-parent'); // REMOVED
      console.log("Drop target: (over item) set to parent of", overId);

      // Logic for setting ghost placeholder when over an 'item'
      if (activeItem && overId !== activeItem.id) {
        const parentInfo = findParentOfItem(outline, overId);
        let ghostDepth = 0;
        if (parentInfo.parent) {
          // To find depth of parent, we need to traverse again or ensure items have depth property
          // For simplicity, assuming parent depth can be found or is known.
          // This needs a proper recursive way to find depth of any item.
          // For now, let's assume a way to get parentDepth.
          // const parentDepth = findItemById(outline, parentInfo.parent.id)?.depth || 0; // Needs item to have depth
          // For this example, we'll simplify depth calculation.
          // If dropping into a parent, depth is parent's depth + 1.
          // This part needs proper depth calculation based on the actual outline structure.
          // Placeholder:
          const parentNode = findItemById(outline, overId); // If dropping ONTO this item as parent
          if (parentNode) {
             // This is not quite right, findItemById doesn't give depth directly unless item stores it.
             // We need a helper: getItemDepth(outline, parentNode.id)
             // For now, if it's a child drop, target item is the parent.
             // Depth will be target item's depth + 1.
             // This requires items to have depth, or calculate it recursively.
             // Let's assume top-level items have depth 0, children depth 1 etc.
             // This part of depth calculation is crucial and needs to be accurate.
             // Simplified for now:
             const getDepth = (items: OutlineItemType[], id: string, currentDepth = 0): number => {
                for (const i of items) {
                    if (i.id === id) return currentDepth;
                    if (i.children) {
                        const foundDepth = getDepth(i.children, id, currentDepth + 1);
                        if (foundDepth !== -1) return foundDepth;
                    }
                }
                return -1;
             };
             const targetDepth = getDepth(outline, overId);
             ghostDepth = targetDepth + 1; // if dropping as child
             setDraggedItemGhostInfo({ parentId: overId, index: (parentNode.children || []).length, depth: ghostDepth });
          }
        } else { // Dropping at root level (before/after a root item)
            // This needs to handle before/after for root items
            // Simplified: if target is root, ghost is root.
            // ghostDepth = 0; // Needs refinement for before/after
        }
      } else {
        setDraggedItemGhostInfo(null);
      }
      return;
    }
    // Clear ghost if not over a valid droppable area for outline items
    // or if specific conditions for ghost are not met.
    // This part is important to hide the ghost when not needed.
    if (!overData || (overData.type !== 'dropzone' && overData.type !== 'item')) {
        setDraggedItemGhostInfo(null);
    }
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    setDraggedItemGhostInfo(null); // Clear ghost on drag end
    const { active, over } = event;

    // --- Integration for Note Linking (Full notes from NoteList) ---
    if (over && active.data.current?.type === 'note' && over.data.current?.type === 'outline-item-note-drop-target') {
      const noteId = active.id.toString().replace('note-', '');
      const targetOutlineItemId = over.data.current.outlineItemId as string;

      if (noteId && targetOutlineItemId) {
        onLinkNoteToOutlineItem(noteId, targetOutlineItemId);
        console.log(`Note link DND: Linking note ${noteId} to outline item ${targetOutlineItemId}`);
      } else {
        console.error("Note link DND (full note): Missing noteId or targetOutlineItemId.", { active, over });
      }
      // Common cleanup for handled DND operations
      setActiveId(null);
      setActiveItem(null);
      isDraggingRef.current = false;
      document.body.style.cursor = '';
      document.body.classList.remove('dragging-mode');
      document.documentElement.classList.remove('outline-dragging-active');
      document.body.classList.remove('disable-websocket-updates');
      window.dispatchEvent(new CustomEvent('reconnect-websocket'));
      if ((window as any).dragResetTimer) {
        clearTimeout((window as any).dragResetTimer);
        (window as any).dragResetTimer = null;
      }
      return;
    }

    // --- Integration for Note Preview Re-linking ---
    if (over && active.data.current?.type === 'note-preview' && over.data.current?.type === 'outline-item-note-drop-target') {
      const noteId = active.data.current.noteId as string;
      const originalOutlineItemId = active.data.current.originalOutlineItemId as string;
      const targetOutlineItemId = over.data.current.outlineItemId as string;

      if (noteId && originalOutlineItemId && targetOutlineItemId) {
        if (originalOutlineItemId !== targetOutlineItemId) {
          onLinkNoteToOutlineItem(noteId, targetOutlineItemId);
          console.log(`Note preview DND: Re-linking note ${noteId} from ${originalOutlineItemId} to ${targetOutlineItemId}`);
        } else {
          console.log(`Note preview DND: Note ${noteId} dropped on its current outline item ${originalOutlineItemId}. No action taken.`);
          // Potentially handle reordering of notes within the same outline item here in the future.
        }
      } else {
        console.error("Note preview DND: Missing noteId, originalOutlineItemId, or targetOutlineItemId.", { active, over });
      }
      // Common cleanup
      setActiveId(null);
      setActiveItem(null);
      isDraggingRef.current = false;
      document.body.style.cursor = '';
      document.body.classList.remove('dragging-mode');
      document.documentElement.classList.remove('outline-dragging-active');
      document.body.classList.remove('disable-websocket-updates');
      window.dispatchEvent(new CustomEvent('reconnect-websocket'));
      if ((window as any).dragResetTimer) {
        clearTimeout((window as any).dragResetTimer);
        (window as any).dragResetTimer = null;
      }
      return;
    }
    // --- End of Integration for Note Preview Re-linking ---
    
    // DETAILED DEBUGGING: Log what we know about the drag event for Outline Reordering
    console.log("========== DRAG END EVENT (Outline Reorder) ==========");
    console.log("Active ID:", active.id);
    console.log("Active item data:", active.data.current);
    
    if (over) {
      console.log("Over ID:", over.id);
      console.log("Over item data:", over.data.current);
      console.log("Is this a valid drop? Active ID !== Over ID:", active.id !== over.id);
      
      // Get actual DOM elements if possible
      const activeElement = document.querySelector(`[data-id="${active.id}"]`);
      const overElement = document.querySelector(`[data-id="${over.id}"]`);
      
      console.log("Active element found in DOM:", !!activeElement);
      console.log("Over element found in DOM:", !!overElement);
      
      if (overElement) {
        console.log("Over element classes:", overElement.className);
        console.log("Over element parent:", overElement.parentElement?.tagName);
      }
    } else {
      console.log("No over element detected - dropped outside valid target");
    }
    
    console.log("Current dropTarget state:", dropTarget); // dropTarget state is still used in handleDragEnd
    console.log("==================================");
    
    // Clean up ALL visual styles - REMOVED specific drop-target classes
    // document.querySelectorAll('.drop-target-before, .drop-target-after, .drop-target-parent').forEach(el => {
    //   el.classList.remove('drop-target-before', 'drop-target-after', 'drop-target-parent');
    // });
    document.body.classList.remove('dragging-mode'); // Keep general dragging mode cleanup
    document.documentElement.classList.remove('outline-dragging-active'); // Keep general dragging mode cleanup
    
    // Re-enable WebSocket updates
    document.body.classList.remove('disable-websocket-updates');
    console.log("WebSocket updates ENABLED after drag operation");
    
    // Force WebSocket reconnect by triggering a custom event
    window.dispatchEvent(new CustomEvent('reconnect-websocket'));
    
    // Mark drag as complete
    isDraggingRef.current = false;
    
    // Reset cursor explicitly for the entire document
    document.body.style.cursor = '';
    document.documentElement.style.cursor = '';
    
    // Clear the safety timer if it exists
    if ((window as any).dragResetTimer) {
      clearTimeout((window as any).dragResetTimer);
      (window as any).dragResetTimer = null;
    }
    
    // Clean up any dragging elements
    document.querySelectorAll('.dragging').forEach(el => {
      el.classList.remove('dragging');
    });
    
    // Always reset state after drag, regardless of outcome, but with a longer delay
    setTimeout(() => {
      setActiveId(null);
      setActiveItem(null);
      setDropTarget(null);
    }, 200);
    
    // Basic validation
    if (!active.id || !over) {
      console.log("Invalid drag end - missing active item or drop target");
      return;
    }

    // Don't allow dropping an item on itself
    if (active.id === over.id) {
      console.log("Cannot drop item on itself");
      return;
    }
    
    // Determine drop target from over element or existing dropTarget state
    let finalDropTarget = dropTarget;

    if (!finalDropTarget) {
      // Create drop target from over element
      const overData = over.data.current as any;
      let targetId = over.id as string;
      let targetType: 'before' | 'after' | 'parent' = 'after';

      if (overData && overData.type === 'dropzone') {
        // Explicit dropzone
        targetId = overData.target;
        targetType = overData.action;
      } else if (targetId.startsWith('before-')) {
        targetId = targetId.replace('before-', '');
        targetType = 'before';
      } else if (targetId.startsWith('after-')) {
        targetId = targetId.replace('after-', '');
        targetType = 'after';
      } else if (targetId.startsWith('child-')) {
        targetId = targetId.replace('child-', '');
        targetType = 'parent';
      } else {
        // Default: treat as dropping after the target item
        targetType = 'after';
      }

      finalDropTarget = { id: targetId, type: targetType };
    }

    console.log(`Moving item ${active.id} to ${finalDropTarget.type} of ${finalDropTarget.id}`);

    // Perform the move operation
    const activeId = active.id as string;
    const { items: newOutline, removed } = removeItem(outline, activeId);

    if (!removed) {
      console.error("Failed to remove item from outline");
      return;
    }

    let result;
    if (finalDropTarget.type === 'parent') {
      result = addItemAsChild(newOutline, finalDropTarget.id, removed);
    } else if (finalDropTarget.type === 'after') {
      result = addItemAfter(newOutline, finalDropTarget.id, removed);
    } else {
      result = addItemBefore(newOutline, finalDropTarget.id, removed);
    }

    // Update the outline directly
    const updatedOutline = calculateOutlineNumbers(result);

    // Simply update the outline - the auto-save will handle persistence
    onOutlineChange(updatedOutline);

    // 1. Reset dragging state
    isDraggingRef.current = false;
    
    // 2. Reset cursor and other visual indications
    document.body.style.cursor = '';
    document.documentElement.style.cursor = '';
    document.body.classList.remove('dragging-mode');
    document.documentElement.classList.remove('outline-dragging-active');
    
    // 3. Reset active data with a slight delay to avoid React state conflicts
    setTimeout(() => {
      setActiveId(null);
      setActiveItem(null);
      setDropTarget(null);
    }, 0);
    
    // 4. Reset state after drag completion
    requestAnimationFrame(() => {
      setTimeout(() => {
        console.log("DRAG COMPLETE - State reset");
      }, 150);
    });
    
    // Notify the minimap that the outline has changed
    if (contentRef.current) {
      const event = new CustomEvent('outlineChanged');
      contentRef.current.dispatchEvent(event);
    }
  };

  return (
    <div className="panel relative flex-1 flex flex-col overflow-hidden">
      {/* Fixed header - styled like notes panel */}
      <div className="bg-background flex justify-between items-center px-3 py-2 border-b border-border z-10">
        <h2 className="font-medium text-sm sm:text-base text-foreground flex items-center">
          <i className="ri-list-check mr-1.5 text-muted-foreground"></i>
          <span>Outline</span>
        </h2>
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs border-border px-2"
            title="Add new outline section"
            onClick={() => {
              onAddItem();
              // Notify minimap that outline will change
              if (contentRef.current) {
                setTimeout(() => {
                  const event = new CustomEvent('outlineChanged');
                  contentRef.current?.dispatchEvent(event);
                }, 100); // Short delay to allow DOM to update
              }
            }}
          >
            <i className="ri-add-line text-sm"></i>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs px-2"
            title="Import outline from document"
            onClick={onImportOutline}
          >
            <i className="ri-download-2-line text-sm"></i>
          </Button>
          {isPoppedOut ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop in panel"
              onClick={() => window.close()}
            >
              <i className="ri-login-box-line text-sm"></i>
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop out panel"
              onClick={popOutPanel}
            >
              <i className="ri-external-link-line text-sm"></i>
            </Button>
          )}
        </div>
      </div>
      {/* Add Search Input here, potentially in its own div for layout control */}
      <div className="p-2 border-b border-border">
        <div className="flex items-center gap-2">
          <div className="relative flex-grow">
            <i className="ri-search-line absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground text-xs z-10"></i>
            <Input
              type="text"
              placeholder="Search Outline & Notes"
              className="w-full rounded-md border-border pl-6 pr-10 text-xs h-7 bg-background text-foreground"
              value={outlineSearchTerm}
              onChange={(e) => {
                setOutlineSearchTerm(e.target.value);
                // setSearchResults will be handled by a useEffect hook watching outlineSearchTerm
                // setCurrentSearchResultIndex(-1); // Reset index on new search
              }}
            />
            {outlineSearchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={() => {
                  setOutlineSearchTerm("");
                  setSearchResults([]);
                  setCurrentSearchResultIndex(-1);
                }}
                title="Clear search"
              >
                <i className="ri-close-line text-sm"></i>
              </Button>
            )}
          </div>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 border-border"
            onClick={() => {
              if (searchResults.length > 0) {
                setCurrentSearchResultIndex((prevIndex) =>
                  prevIndex <= 0 ? searchResults.length - 1 : prevIndex - 1
                );
              }
            }}
            disabled={searchResults.length === 0}
            title="Previous result"
          >
            <i className="ri-arrow-up-s-line text-sm"></i>
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 border-border"
            onClick={() => {
              if (searchResults.length > 0) {
                setCurrentSearchResultIndex((prevIndex) =>
                  prevIndex >= searchResults.length - 1 ? 0 : prevIndex + 1
                );
              }
            }}
            disabled={searchResults.length === 0}
            title="Next result"
          >
            <i className="ri-arrow-down-s-line text-sm"></i>
          </Button>
        </div>
      </div>
      
      {/* Scrollable content area with minimap */}
      <div className="flex-1 overflow-hidden flex">
        <div 
          className="relative flex-1 overflow-auto p-3 pt-2"
          ref={contentRef}
          onMouseLeave={() => setHoveredItemPath([])}
        >

          
          <DndContext
            sensors={sensors}
            collisionDetection={closestCorners}
            onDragStart={(event) => {
              // Add a flag to completely disable WebSocket connections
              window.localStorage.setItem('disable_websocket', 'true');
              console.log("WEBSOCKET: Completely disabled during drag");
              // Call the normal handler
              handleDragStart(event); // Correctly assigned
            }}
            onDragOver={handleDragOver} // Correctly assigned
            onDragEnd={(event) => {
              // Re-enable WebSocket connections
              window.localStorage.removeItem('disable_websocket');
              console.log("WEBSOCKET: Re-enabled after drag end");
              // Call the normal handler
              handleDragEnd(event); // Correctly assigned
            }}
            // Comprehensive cancellation handler that performs extensive cleanup
            onDragCancel={(event) => { // Keep full onDragCancel logic for now
              setDraggedItemGhostInfo(null); // Clear ghost on drag cancel
              // Re-enable WebSocket connections
              window.localStorage.removeItem('disable_websocket');
              console.log("WEBSOCKET: Re-enabled after drag cancel");
              console.log("⚠️ Drag was explicitly cancelled - performing thorough cleanup", event);
              
              // 1. Reset core dragging state
              isDraggingRef.current = false;
              
              // 2. Reset cursor and classes on entire document
              document.body.style.cursor = '';
              document.documentElement.style.cursor = '';
              document.body.classList.remove('dragging-mode');
              document.documentElement.classList.remove('outline-dragging-active');
              document.body.classList.remove('disable-websocket-updates');
              console.log("WebSocket updates ENABLED after drag cancellation");
              
              // Force WebSocket reconnect by triggering a custom event
              window.dispatchEvent(new CustomEvent('reconnect-websocket'));
              
              // 3. Reset React state with a slight delay to avoid React state update conflicts
              setTimeout(() => {
                setActiveId(null);
                setActiveItem(null);
                setDropTarget(null);
              }, 0);
              
              // 4. Remove all visual indicators with detailed logging - REMOVED specific drop-target classes
              // const beforeElements = document.querySelectorAll('.drop-target-before');
              // const afterElements = document.querySelectorAll('.drop-target-after');
              // const parentElements = document.querySelectorAll('.drop-target-parent');
              const draggingElements = document.querySelectorAll('.dragging');
              
              // console.log(`Removing visual indicators: ${beforeElements.length} before, ${afterElements.length} after, ${parentElements.length} parent, ${draggingElements.length} dragging`);
              console.log(`Removing visual indicators: ${draggingElements.length} dragging`);
              
              // Process in separate ticks to allow DOM to update properly
              setTimeout(() => {
                // beforeElements.forEach(el => el.classList.remove('drop-target-before'));
                // afterElements.forEach(el => el.classList.remove('drop-target-after'));
                // parentElements.forEach(el => el.classList.remove('drop-target-parent'));
                draggingElements.forEach(el => el.classList.remove('dragging'));
                
                console.log("Visual indicators (dragging class) removed");
              }, 0);
              
              // 5. Clear any timers
              if ((window as any).dragResetTimer) {
                clearTimeout((window as any).dragResetTimer);
                (window as any).dragResetTimer = null;
                console.log("Cleared drag reset timer");
              }
              
              // 6. Force rerender after cleanup
              setTimeout(() => {
                console.log("Drag cancellation cleanup completed");
                // Trigger a small update to force React to rerender cleanly
                setHoveredItemPath([...hoveredItemPath]);
              }, 100);
            }}
          >
            <SortableContext 
              items={outlineWithGhost.map(item => item.id)} // Use outlineWithGhost, which is now based on filteredOutline if search is active
              strategy={verticalListSortingStrategy}
            >
              <div className="relative min-h-[40px] outline-list-container overflow-visible">
                {/* Top-level drop zone placeholder - removed */}
                
                {outlineWithGhost.length > 0 ? (
                  <div className="space-y-0.5">
                    {outlineWithGhost.map((item, index) => {
                      const isGhost = item.id === 'ghost-item-id';
                      const currentItemDepth = isGhost && draggedItemGhostInfo ? draggedItemGhostInfo.depth : 0;
                      return (
                        <OutlineItem
                          key={isGhost ? 'ghost-item-id' : item.id}
                          item={item}
                          isGhost={isGhost}
                          index={index} // Index within outlineWithGhost
                          activeId={activeId} // Pass activeId
                          depth={currentItemDepth}
                          hoveredItemPath={hoveredItemPath} // Correctly pass the state
                          onHover={(itemId) => {
                            // Path finding should ideally work on the currently displayed structure,
                            // but for simplicity, if search is active, this might need adjustment
                            // or be accepted as a limitation if paths are based on original outline.
                            // For now, using `outlineToUseForGhost` for path finding might be more consistent.
                            const path = findPathToItem(outlineToUseForGhost, itemId);
                            setHoveredItemPath(path);
                          }}
                          onUpdate={(updatedItem) => {
                            // When updating, we need to ensure the update is reflected in the original `outline` data,
                            // as `filteredOutline` is a derived state.
                            // This might require finding the item in the original `outline` and updating it there,
                            // then letting the filtering re-run.
                            const updateInOriginalOutline = (items: OutlineItemType[], updated: OutlineItemType): OutlineItemType[] => {
                              return items.map(i => {
                                if (i.id === updated.id) return updated;
                                if (i.children) return { ...i, children: updateInOriginalOutline(i.children, updated) };
                                return i;
                              });
                            };
                            if (!isGhost) {
                              const newOriginalOutline = updateInOriginalOutline(outline, updatedItem);
                              onOutlineChange(newOriginalOutline);
                            } else {
                              console.warn("Attempted to update a ghost item");
                            }
                            // Notify minimap, etc.
                            if (contentRef.current) {
                              const event = new CustomEvent('outlineChanged');
                              contentRef.current.dispatchEvent(event);
                            }
                          }}
                          onDelete={() => {
                            if (isGhost) return;
                            // Deletion should also operate on the original outline structure.
                            const notesLinked = notes.filter(note => note.linkedOutlineId === item.id);
                            if (notesLinked.length > 0) {
                              setOutlineItemToDeleteId(item.id);
                              setIsConfirmDeleteDialogOpen(true);
                            } else {
                              onDeleteOutlineItemWithNotes(item.id); // This should correctly modify the original outline
                            }
                            if (contentRef.current) {
                              const event = new CustomEvent('outlineChanged');
                              contentRef.current.dispatchEvent(event);
                            }
                          }}
                          onAddChild={() => {
                            if (!isGhost) {
                              // Adding a child should also operate on the original outline item's ID.
                              onAddItem(item.id);
                            }
                            if (contentRef.current) {
                              setTimeout(() => {
                                const event = new CustomEvent('outlineChanged');
                                contentRef.current?.dispatchEvent(event);
                              }, 100);
                            }
                          }}
                          allNotes={notes}
                          // Note Handler Props:
                          onAddNote={isGhost ? () => {} : handleAddNoteToOutlineItem}
                          // onOpenNoteEditor removed from here
                          onLinkNoteToOutlineItem={isGhost ? () => {} : onLinkNoteToOutlineItem}
                          onNoteDelete={isGhost ? () => {} : onNoteDelete}
                          onNoteDuplicate={isGhost ? () => {} : onNoteDuplicate}
                          onDeleteOutlineItemWithNotes={isGhost ? () => {} : onDeleteOutlineItemWithNotes}
                          // Pass down the new props for inline editing
                          onNoteUpdate={isGhost ? () => {} : onNoteUpdate}
                          onFileUpload={isGhost ? async () => "" : onFileUpload} // Simplified: pass directly
                          allOutlineItems={outlineToUseForGhost} // Pass the potentially filtered list
                          // Search result highlighting props
                          isCurrentSearchResult={!isGhost && searchResults.length > 0 && searchResults[currentSearchResultIndex]?.item.id === item.id}
                          currentSearchResultMatchDetails={!isGhost && searchResults.length > 0 && searchResults[currentSearchResultIndex]?.item.id === item.id ? searchResults[currentSearchResultIndex].matchDetails : undefined}
                          searchTerm={outlineSearchTerm}
                        />
                      );
                    })}
                  </div>
                ) : (
                  // Display different message if search is active and yields no results vs. outline is genuinely empty.
                  outlineSearchTerm && filteredOutline.length === 0 && outline.length > 0 ? ( // This filteredOutline check might need to be searchResults.length === 0
                    <div className="text-center py-6 text-neutral-500 border border-dashed border-neutral-300 rounded-md bg-neutral-50/50 my-2">
                      <div className="flex flex-col items-center justify-center gap-2">
                        <i className="ri-search-eye-line text-2xl text-neutral-400"></i>
                        <p className="font-medium">No results found</p>
                        <p className="text-sm text-neutral-400">Try adjusting your search term.</p>
                      </div>
                    </div>
                  ) : (
                    // Original empty state or ghost item display logic
                    draggedItemGhostInfo && activeItem ? (
                      <div className="space-y-0.5">
                        <OutlineItem
                          key="ghost-item-id"
                          item={{...activeItem, id: 'ghost-item-id', number: "👻"}}
                          isGhost={true}
                          index={0} // Placeholder index
                          activeId={activeId}
                          depth={draggedItemGhostInfo.depth}
                          hoveredItemPath={[]}
                          onHover={() => {}}
                          onUpdate={() => {}}
                          onDelete={() => {}}
                          onAddChild={() => {}}
                          allNotes={notes} // Pass actual notes
                          onAddNote={() => {}}
                          // onOpenNoteEditor removed
                          onLinkNoteToOutlineItem={() => {}}
                          onNoteDelete={() => {}}
                          onNoteDuplicate={() => {}}
                          onDeleteOutlineItemWithNotes={() => {}}
                          // Pass down the new props for inline editing
                          onNoteUpdate={() => {}}
                          onFileUpload={async () => ""} // Ghost's onFileUpload returns a Promise<string>
                          allOutlineItems={outlineToUseForGhost} // Pass the potentially filtered list
                           // Search result highlighting props for ghost
                          isCurrentSearchResult={false}
                          currentSearchResultMatchDetails={undefined}
                          searchTerm={outlineSearchTerm}
                        />
                      </div>
                    ) : (
                      <div className="text-center py-6 text-neutral-500 border border-dashed border-neutral-300 rounded-md bg-neutral-50/50 my-2">
                        <div className="flex flex-col items-center justify-center gap-2">
                          <i className="ri-file-list-line text-2xl text-neutral-400"></i>
                          <p className="font-medium">Your outline is empty</p>
                          <p className="text-sm text-neutral-400">Click "Add" to create your first outline item</p>
                        </div>
                      </div>
                    )
                  )
                )}
              </div>
              
              {/* Enhanced Drag overlay with better performance */}
              <DragOverlay 
                dropAnimation={{
                  duration: 150,
                  easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
                  sideEffects: defaultDropAnimationSideEffects({
                    styles: {
                      active: {
                        opacity: '0.4',
                      },
                    },
                  }),
                }}
              >
                {activeItem ? (
                  <div className="px-4 py-2 bg-white border-2 border-blue-400 rounded-md shadow-xl max-w-[300px] text-ellipsis overflow-hidden whitespace-nowrap z-[1000] pointer-events-none">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-600 font-mono text-xs min-w-fit pr-1.5 font-bold">{activeItem.number}</span>
                      <span className="font-medium text-sm truncate">{activeItem.title}</span>
                    </div>
                  </div>
                ) : null}
              </DragOverlay>
            </SortableContext>
          </DndContext>
          
          {/* Visual indicators for drop targets have been REMOVED */}
          {/* {dropTarget && (
            <>
              <div id="drop-indicator" className="absolute pointer-events-none" style={{ display: 'none' }}>
                {dropTarget.type === 'before' && (
                  <div className="h-0.5 w-full bg-blue-500 rounded-full animate-pulse" />
                )}
                {dropTarget.type === 'after' && (
                  <div className="h-0.5 w-full bg-blue-500 rounded-full animate-pulse" />
                )}
                {dropTarget.type === 'parent' && (
                  <div className="border-2 border-blue-500 rounded-md animate-pulse" style={{ position: 'absolute' }} />
                )}
              </div>
            </>
          )} */}
        </div>
        
        {/* Fixed width sidebar containing the minimap */}
        <div className="w-[80px] h-full sticky top-0 border-l border-neutral-100 z-20">
          <NewMinimap
            contentRef={contentRef}
            highlightedItemId={hoveredItemPath.length > 0 ? hoveredItemPath[hoveredItemPath.length - 1] : null}
            activeParentItemIds={hoveredItemPath.length > 1 ? hoveredItemPath.slice(0, -1) : []}
          />
        </div>
      </div>

      {/* Note Editor Modal Removed */}
    </div>
  );
}