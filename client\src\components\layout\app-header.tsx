import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLocation } from "wouter";
import { useAuth } from '@/hooks/use-auth';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserPreferencesModal } from '@/components/user-preferences-modal';

interface AppHeaderProps {
  showLoginButtons?: boolean;
  showUserMenu?: boolean;
  showDocActions?: boolean;
  onDocuments?: () => void;
  onSpellCheck?: () => void;
  onGrammarCheck?: () => void;
  onShare?: () => void;
  onReferences?: () => void;
}

export function AppHeader({ 
  showLoginButtons = false, 
  showUserMenu = true,
  showDocActions = false,
  onDocuments,
  onSpellCheck,
  onGrammarCheck,
  onShare,
  onReferences
}: AppHeaderProps) {
  const [, navigate] = useLocation();
  const { user, logoutMutation } = useAuth();
  const [isPreferencesOpen, setIsPreferencesOpen] = useState(false);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleOpenPreferences = () => {
    setIsPreferencesOpen(true);
  };

  return (
    <header className="border-b border-border bg-background shadow-sm py-3 px-4">
      <div className="container mx-auto flex items-center justify-between text-foreground">
        <div className="flex items-center gap-2">
          <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M16 2H8C4.691 2 2 4.691 2 8v13a1 1 0 0 0 1 1h13c3.309 0 6-2.691 6-6V8c0-3.309-2.691-6-6-6zm4 14c0 2.206-1.794 4-4 4H4V8c0-2.206 1.794-4 4-4h8c2.206 0 4 1.794 4 4v8z"/>
            <path d="M7 14.987v2a1 1 0 0 0 1 1h2a1 1 0 0 0 .707-.293l6.4-6.4a.999.999 0 0 0 0-1.414l-2-2a.999.999 0 0 0-1.414 0l-6.4 6.4A1 1 0 0 0 7 14.987zm3-5.274 1.293 1.293-4.986 4.986H8c-1.103 0-2-.897-2-2v-1.294l4-3.985z"/>
          </svg>
          <h1 className="text-xl font-semibold text-foreground">Inspire</h1>
          <div className="hidden md:block">
            <Badge variant="outline" className="text-xs text-primary-600 bg-primary-50 hover:bg-primary-100">
              Professional
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {showDocActions && (
            <>
              <Button 
                variant="ghost" 
                size="sm" 
                className="flex items-center gap-1 text-muted-foreground hover:text-primary"
                onClick={onDocuments}
              >
                <i className="ri-dashboard-line"></i>
                <span>Documents</span>
              </Button>
              
              <div className="h-5 border-r border-border"></div>


              
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-muted-foreground hover:text-primary"
                onClick={onReferences}
              >
                <i className="ri-book-2-line"></i>
                <span>References</span>
              </Button>

              {onShare && (
                <>
                  <div className="h-5 border-r border-border"></div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-1 text-muted-foreground hover:text-primary"
                    onClick={onShare}
                  >
                    <i className="ri-share-line"></i>
                    <span>Share</span>
                  </Button>
                </>
              )}
              
              <div className="h-5 border-r border-border"></div>
            </>
          )}

          {showLoginButtons && (
            <>
              <Button 
                variant="ghost" 
                onClick={() => navigate("/auth")}
                size="sm"
              >
                Login
              </Button>
              <Button 
                onClick={() => navigate("/auth")}
                size="sm"
              >
                Register
              </Button>
            </>
          )}

          {showUserMenu && user && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 rounded-full" aria-label="User menu">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary-100 text-primary-800">
                      {user?.username.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  <div className="flex flex-col">
                    <span>My Account</span>
                    <span className="text-xs text-muted-foreground">{user?.username}</span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => navigate("/documents")}>
                  <i className="ri-dashboard-line mr-2"></i>
                  <span>Documents</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleOpenPreferences}>
                  <i className="ri-settings-line mr-2"></i>
                  <span>Preferences</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <i className="ri-logout-box-line mr-2"></i>
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
      
      {/* Preferences Modal */}
      <UserPreferencesModal
        isOpen={isPreferencesOpen}
        onClose={() => setIsPreferencesOpen(false)}
      />
    </header>
  );
}